// Package rebate provides backward compatibility aliases for agent referral functionality.
// This package is deprecated. Use internal/service/agent_referral instead.
package rebate

import (
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/agent_referral"
)

// Deprecated: Use agent_referral.NewAgentReferralService() instead.
// NewInvitationService creates a new agent referral service instance.
// This function is kept for backward compatibility.
func NewInvitationService() *agent_referral.AgentReferralService {
	return agent_referral.NewAgentReferralService()
}
